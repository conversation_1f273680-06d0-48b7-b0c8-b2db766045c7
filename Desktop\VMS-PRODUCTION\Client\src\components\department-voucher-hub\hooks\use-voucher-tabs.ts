
import { useState, useEffect } from 'react';
import { Department, Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export const useVoucherTabs = (department: Department) => {
  const vouchers = useAppStore((state) => state.vouchers);
  const fetchVouchers = useAppStore((state) => state.fetchVouchers);
  const [activeTab, setActiveTab] = useState('new-vouchers');

  // Fetch vouchers on department change - add fetchVouchers to dependencies to prevent stale closure
  useEffect(() => {
    console.log(`🔄 VOUCHER HUB: Fetching ALL vouchers for ${department} hub (need to filter by originalDepartment)`);
    fetchVouchers('ALL'); // Fetch ALL vouchers, then filter by originalDepartment in frontend
  }, [department, fetchVouchers]);

  // DEBUG: Log voucher count and target voucher presence
  useEffect(() => {
    console.log(`📊 VOUCHER HUB DEBUG for ${department}:`);
    console.log(`  Total vouchers in store: ${vouchers.length}`);

    const targetVoucher = vouchers.find(v => v.voucherId === 'FINJUL0003-COPY');
    if (targetVoucher) {
      console.log(`  ✅ Target voucher FOUND in store:`, targetVoucher);
    } else {
      console.log(`  ❌ Target voucher NOT FOUND in store`);
      console.log(`  Available vouchers:`, vouchers.map(v => v.voucherId));
    }

    console.log(`  New vouchers for ${department}: ${newVouchers.length}`);
    console.log(`  New voucher IDs:`, newVouchers.map(v => v.voucherId));
  }, [vouchers, department, newVouchers]);

  // Debug logging for voucher counts
  useEffect(() => {
    console.log(`📊 VOUCHER HUB [${department}]: Total vouchers: ${vouchers.length}`);
    console.log(`📊 VOUCHER HUB [${department}]: Dispatched vouchers: ${vouchers.filter(v =>
      v.originalDepartment === department &&
      (v.dispatched === true || v.auditDispatchedBy || v.auditDispatchTime)
    ).length}`);
  }, [vouchers, department]);

  // Removed problematic useEffect calls that were causing infinite loops

  // RESUBMISSION WORKFLOW FIX: Include resubmitted vouchers in NEW VOUCHER tab
  const newVouchers = vouchers.filter(v => {
    // For audit dashboard viewing department hubs (e.g., FINANCE VOUCHER HUB)
    const basicCriteria = v.originalDepartment === department &&
                         v.department === 'AUDIT' &&
                         v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
                         v.receivedByAudit === true &&
                         !v.dispatched &&
                         !v.deleted; // Exclude deleted vouchers

    // RESUBMISSION FIX: For resubmitted vouchers, ignore workStarted status
    // They should appear in NEW VOUCHER tab regardless of previous work status
    const isResubmittedVoucher = v.resubmissionCount && v.resubmissionCount > 0;

    // DEBUG: Log filtering for FINJUL0003-COPY specifically
    if (v.voucherId === 'FINJUL0003-COPY') {
      console.log(`🔍 NEW VOUCHER FILTERING DEBUG for ${v.voucherId}:`);
      console.log(`  originalDepartment === ${department}: ${v.originalDepartment === department} (${v.originalDepartment})`);
      console.log(`  department === 'AUDIT': ${v.department === 'AUDIT'} (${v.department})`);
      console.log(`  status === 'AUDIT PROCESSING': ${v.status === VOUCHER_STATUSES.AUDIT_PROCESSING} (${v.status})`);
      console.log(`  receivedByAudit === true: ${v.receivedByAudit === true} (${v.receivedByAudit})`);
      console.log(`  !dispatched: ${!v.dispatched} (${v.dispatched})`);
      console.log(`  !deleted: ${!v.deleted} (${v.deleted})`);
      console.log(`  basicCriteria: ${basicCriteria}`);
      console.log(`  resubmissionCount: ${v.resubmissionCount}`);
      console.log(`  isResubmittedVoucher: ${isResubmittedVoucher}`);
      console.log(`  workStarted: ${v.workStarted}`);
    }

    if (isResubmittedVoucher) {
      const result = basicCriteria; // Resubmitted vouchers always go to NEW VOUCHER tab
      if (v.voucherId === 'FINJUL0003-COPY') {
        console.log(`  RESUBMITTED VOUCHER RESULT: ${result}`);
      }
      return result;
    } else {
      const result = basicCriteria && v.workStarted !== true; // Regular vouchers need workStarted = false
      if (v.voucherId === 'FINJUL0003-COPY') {
        console.log(`  REGULAR VOUCHER RESULT: ${result}`);
      }
      return result;
    }
  });

  // DUAL-TAB WORKFLOW: PENDING DISPATCH = regular vouchers + dispatchable rejected copies
  const pendingDispatchVouchers = vouchers.filter(v => {
    // RESUBMISSION FIX: Exclude resubmitted vouchers from PENDING DISPATCH
    // They should stay in NEW VOUCHER tab until work is started on them
    const isResubmittedVoucher = v.resubmissionCount && v.resubmissionCount > 0;
    if (isResubmittedVoucher && v.workStarted !== true) {
      return false; // Resubmitted vouchers without work started belong in NEW VOUCHER tab
    }

    // Regular vouchers with work started
    const regularVouchers = v.originalDepartment === department &&
                           v.department === 'AUDIT' &&
                           v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
                           v.receivedByAudit === true &&
                           v.workStarted === true &&
                           !v.dispatched &&
                           !v.deleted &&
                           v.rejectionType !== 'PERMANENT_RECORD'; // Exclude permanent records

    // DUAL-TAB WORKFLOW: Include dispatchable rejected voucher copies
    const dispatchableRejectedCopies = v.originalDepartment === department &&
                                      v.department === 'AUDIT' &&
                                      v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
                                      v.rejectionType === 'DISPATCHABLE_COPY' &&
                                      (v.isRejectionCopy === true || v.isRejectionCopy === 1) &&
                                      v.workStarted === true &&
                                      !v.dispatched &&
                                      !v.deleted;

    return regularVouchers || dispatchableRejectedCopies;
  });
  // PERMANENT FIX: DISPATCHED tab shows only CERTIFIED vouchers returned from Audit
  const dispatchedVouchers = vouchers.filter(v => {
    const isOriginalDepartment = v.originalDepartment === department;
    const isDispatchedByAudit = v.auditDispatchedBy && v.auditDispatchTime; // Must be dispatched by Audit
    const isReceivedByDepartment = v.department === department; // Must be received by this department
    const isCertified = v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED; // Must be certified
    const isNotDeleted = !v.deleted;

    // PERMANENT LOGIC: Only show VOUCHER_CERTIFIED vouchers that were:
    // 1. Originally from this department
    // 2. Dispatched by Audit (completed audit process)
    // 3. Received back by this department
    // 4. Have CERTIFIED status (successful audit completion)
    return isOriginalDepartment &&
           isDispatchedByAudit &&
           isReceivedByDepartment &&
           isCertified &&
           isNotDeleted;
  });

  const returnedVouchers = vouchers.filter(v =>
    v.originalDepartment === department &&
    v.status === VOUCHER_STATUSES.VOUCHER_RETURNED &&
    !v.deleted // Exclude deleted vouchers
  );

  const rejectedVouchers = vouchers.filter(v => {
    const isRejectedStatus = v.status === VOUCHER_STATUSES.VOUCHER_REJECTED;
    const isNotDeleted = !v.deleted;

    if (!isRejectedStatus || !isNotDeleted) {
      return false;
    }

    // DUAL-TAB WORKFLOW: Show rejected vouchers based on rejection type and department context

    if (department === 'AUDIT') {
      // For Audit department: Show PERMANENT RECORDS that are currently in AUDIT
      // (regardless of original department - they moved to AUDIT for audit trail)
      const isCurrentlyInAudit = v.department === 'AUDIT';
      const isPermanentRecord = v.rejectionType === 'PERMANENT_RECORD';
      return isCurrentlyInAudit && isPermanentRecord;
    } else {
      // PERMANENT FIX: For Finance department - show rejected voucher copies that were dispatched and received

      const isOriginalDepartment = v.originalDepartment === department;
      const isRejectedStatus = v.status === VOUCHER_STATUSES.VOUCHER_REJECTED;
      const isDispatchedByAudit = v.auditDispatchedBy && v.auditDispatchTime; // Must be dispatched by Audit
      const isReceivedByDepartment = v.department === department; // Must be received by this department
      const isRejectionCopy = v.isRejectionCopy === true || v.isRejectionCopy === 1; // Must be dispatchable copy

      // PERMANENT LOGIC: Only show VOUCHER_REJECTED dispatchable copies that were:
      // 1. Originally from this department
      // 2. Dispatched by Audit (rejection workflow completed)
      // 3. Received back by this department
      // 4. Have REJECTED status
      // 5. Are dispatchable copies (not permanent records)
      return isOriginalDepartment &&
             isRejectedStatus &&
             isDispatchedByAudit &&
             isReceivedByDepartment &&
             isRejectionCopy;
    }
  });

  // Tab counts for debugging (removed console logging to prevent loops)

  return {
    activeTab,
    setActiveTab,
    newVouchers,
    pendingDispatchVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers
  };
};
