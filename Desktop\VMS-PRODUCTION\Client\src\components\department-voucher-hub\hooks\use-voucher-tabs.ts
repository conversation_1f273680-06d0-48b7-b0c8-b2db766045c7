
import { useState, useEffect } from 'react';
import { Department, Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export const useVoucherTabs = (department: Department) => {
  const vouchers = useAppStore((state) => state.vouchers);
  const fetchVouchers = useAppStore((state) => state.fetchVouchers);
  const [activeTab, setActiveTab] = useState('new-vouchers');

  // Fetch vouchers on department change - add fetchVouchers to dependencies to prevent stale closure
  useEffect(() => {
    fetchVouchers('ALL'); // Fetch ALL vouchers, then filter by originalDepartment in frontend
  }, [department, fetchVouchers]);

  // Removed problematic useEffect calls that were causing infinite loops

  // SEPARATE WORKFLOW PATHS: NEW VOUCHER tab (Normal + Resubmitted, NO Rejection Copies)
  const newVouchers = vouchers.filter(v => {
    // For audit dashboard viewing department hubs (e.g., FINANCE VOUCHER HUB)
    const check1 = v.originalDepartment === department;
    const check2 = v.department === 'AUDIT';
    const check3 = v.status === VOUCHER_STATUSES.AUDIT_PROCESSING;
    const check4 = v.receivedByAudit === true;
    const check5 = !v.dispatched;
    const check6 = !v.deleted;

    const basicCriteria = check1 && check2 && check3 && check4 && check5 && check6;

    // WORKFLOW SEPARATION: Identify voucher types
    const isNormalVoucher = !v.isRejectionCopy && (v.resubmissionCount === 0 || !v.resubmissionCount);
    const isResubmittedVoucher = v.resubmissionCount && v.resubmissionCount > 0 && !v.isRejectionCopy;
    const isRejectionCopy = v.isRejectionCopy === true || v.isRejectionCopy === 1;

    // NEW VOUCHER tab: Only normal and resubmitted vouchers (NO rejection copies)
    // Rejection copies go directly to PENDING DISPATCH
    return basicCriteria && v.workStarted !== true && (isNormalVoucher || isResubmittedVoucher) && !isRejectionCopy;
  });

  // SEPARATE WORKFLOW PATHS: PENDING DISPATCH tab (Normal + Resubmitted + Rejection Copies)
  const pendingDispatchVouchers = vouchers.filter(v => {
    // Base criteria for all vouchers in PENDING DISPATCH
    const baseCriteria = v.originalDepartment === department &&
                        v.department === 'AUDIT' &&
                        !v.dispatched &&
                        !v.deleted;

    if (!baseCriteria) return false;

    // WORKFLOW SEPARATION: Three distinct paths

    // PATH 1: Normal vouchers with work started
    const isNormalVoucherReady = v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
                                v.receivedByAudit === true &&
                                v.workStarted === true &&
                                !v.isRejectionCopy &&
                                (v.resubmissionCount === 0 || !v.resubmissionCount);

    // PATH 2: Resubmitted vouchers with work started
    const isResubmittedVoucherReady = v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
                                     v.receivedByAudit === true &&
                                     v.workStarted === true &&
                                     v.resubmissionCount && v.resubmissionCount > 0 &&
                                     !v.isRejectionCopy;

    // PATH 3: Rejection copies (ready for dispatch immediately)
    const isRejectionCopyReady = v.rejectionType === 'DISPATCHABLE_COPY' &&
                                (v.isRejectionCopy === true || v.isRejectionCopy === 1) &&
                                v.status === VOUCHER_STATUSES.AUDIT_PROCESSING;

    return isNormalVoucherReady || isResubmittedVoucherReady || isRejectionCopyReady;
  });
  // SEPARATE WORKFLOW PATHS: DISPATCHED tab (Normal + Resubmitted + Dispatched Rejection Copies)
  const dispatchedVouchers = vouchers.filter(v => {
    const isOriginalDepartment = v.originalDepartment === department;
    const isDispatchedByAudit = v.auditDispatchedBy && v.auditDispatchTime;
    const isNotDeleted = !v.deleted;

    // WORKFLOW SEPARATION: Three types can appear in DISPATCHED tab
    const isNormalVoucher = !v.isRejectionCopy && (v.resubmissionCount === 0 || !v.resubmissionCount);
    const isResubmittedVoucher = v.resubmissionCount && v.resubmissionCount > 0 && !v.isRejectionCopy;
    const isDispatchedRejectionCopy = v.isRejectionCopy && v.rejectionType === 'DISPATCHABLE_COPY' && v.dispatched === true;

    const isValidWorkflow = isNormalVoucher || isResubmittedVoucher || isDispatchedRejectionCopy;

    if (department === 'AUDIT') {
      // AUDIT DISPATCHED TAB: Show vouchers dispatched FROM Audit TO other departments
      const isInAuditDepartment = v.department === 'AUDIT';
      const isDispatched = v.dispatched === true;

      // Include both CERTIFIED and REJECTED vouchers (for rejection copies)
      const hasValidStatus = v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED ||
                            v.status === VOUCHER_STATUSES.VOUCHER_REJECTED;

      return isOriginalDepartment &&
             isDispatchedByAudit &&
             isInAuditDepartment &&
             isDispatched &&
             hasValidStatus &&
             isNotDeleted &&
             isValidWorkflow;
    } else {
      // DEPARTMENT DISPATCHED TAB: Show ONLY CERTIFIED vouchers that came back from audit
      const isDispatched = v.dispatched === true;

      // CRITICAL FIX: Only show CERTIFIED vouchers in DISPATCHED tab
      // Rejected vouchers should ONLY appear in REJECTED tab
      const hasValidStatus = v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED;

      return isOriginalDepartment &&
             isDispatchedByAudit &&
             isDispatched &&
             hasValidStatus &&
             isNotDeleted &&
             isValidWorkflow;
    }
  });



  // SEPARATE WORKFLOW PATHS: REJECTED tab (Only Rejection Workflow)
  const rejectedVouchers = vouchers.filter(v => {
    const isRejectedStatus = v.status === VOUCHER_STATUSES.VOUCHER_REJECTED;
    const isNotDeleted = !v.deleted;

    if (!isRejectedStatus || !isNotDeleted) {
      return false;
    }

    // WORKFLOW SEPARATION: Only rejection workflow vouchers (NO resubmitted vouchers)

    if (department === 'AUDIT') {
      // AUDIT REJECTED TAB: Show permanent records only
      const isCurrentlyInAudit = v.department === 'AUDIT';
      const isPermanentRecord = v.rejectionType === 'PERMANENT_RECORD';

      return isCurrentlyInAudit && isPermanentRecord;
    } else {
      // DEPARTMENT REJECTED TAB: Show received rejection copies only
      const isOriginalDepartment = v.originalDepartment === department;
      const isDispatchedByAudit = v.auditDispatchedBy && v.auditDispatchTime;
      const isReceivedByDepartment = v.department === department;
      const isRejectionCopy = v.isRejectionCopy === true || v.isRejectionCopy === 1;

      // WORKFLOW SEPARATION: Only rejection copies (NO resubmitted vouchers)
      return isOriginalDepartment &&
             isRejectedStatus &&
             isDispatchedByAudit &&
             isReceivedByDepartment &&
             isRejectionCopy;
    }
  });

  // SEPARATE WORKFLOW PATHS: RETURNED tab (Normal workflow only)
  const returnedVouchers = vouchers.filter(v => {
    const isOriginalDepartment = v.originalDepartment === department;
    const isReturnedStatus = v.status === VOUCHER_STATUSES.VOUCHER_RETURNED;
    const isNotDeleted = !v.deleted;

    // WORKFLOW SEPARATION: Only normal vouchers (NO rejection copies or resubmitted)
    const isNormalVoucher = !v.isRejectionCopy && (v.resubmissionCount === 0 || !v.resubmissionCount);

    return isOriginalDepartment && isReturnedStatus && isNotDeleted && isNormalVoucher;
  });

  // Tab counts for debugging (removed console logging to prevent loops)

  return {
    activeTab,
    setActiveTab,
    newVouchers,
    pendingDispatchVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers
  };
};
