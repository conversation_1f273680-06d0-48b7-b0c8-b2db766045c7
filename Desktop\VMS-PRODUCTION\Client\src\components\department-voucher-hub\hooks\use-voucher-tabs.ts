
import { useState, useEffect } from 'react';
import { Department, Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export const useVoucherTabs = (department: Department) => {
  const vouchers = useAppStore((state) => state.vouchers);
  const fetchVouchers = useAppStore((state) => state.fetchVouchers);
  const [activeTab, setActiveTab] = useState('new-vouchers');

  // Fetch vouchers on department change - add fetchVouchers to dependencies to prevent stale closure
  useEffect(() => {
    fetchVouchers('ALL'); // Fetch ALL vouchers, then filter by originalDepartment in frontend
  }, [department, fetchVouchers]);

  // Removed problematic useEffect calls that were causing infinite loops

  // RESUBMISSION WORKFLOW FIX: Include resubmitted vouchers in NEW VOUCHER tab
  const newVouchers = vouchers.filter(v => {
    // For audit dashboard viewing department hubs (e.g., FINANCE VOUCHER HUB)
    const check1 = v.originalDepartment === department;
    const check2 = v.department === 'AUDIT';
    const check3 = v.status === VOUCHER_STATUSES.AUDIT_PROCESSING;
    const check4 = v.receivedByAudit === true;
    const check5 = !v.dispatched;
    const check6 = !v.deleted;

    const basicCriteria = check1 && check2 && check3 && check4 && check5 && check6;

    // RESUBMISSION FIX: Resubmitted vouchers follow same workStarted logic as regular vouchers
    // They should only appear in NEW VOUCHER tab when work has NOT started
    const isResubmittedVoucher = v.resubmissionCount && v.resubmissionCount > 0;

    // Both regular and resubmitted vouchers should only appear in NEW VOUCHER when work hasn't started
    return basicCriteria && v.workStarted !== true;
  });

  // DUAL-TAB WORKFLOW: PENDING DISPATCH = regular vouchers + dispatchable rejected copies + resubmitted vouchers with work started
  const pendingDispatchVouchers = vouchers.filter(v => {
    // Base criteria for all vouchers in PENDING DISPATCH
    const baseCriteria = v.originalDepartment === department &&
                        v.department === 'AUDIT' &&
                        v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
                        v.receivedByAudit === true &&
                        v.workStarted === true &&
                        !v.dispatched &&
                        !v.deleted;

    if (!baseCriteria) return false;

    // RESUBMISSION FIX: Include resubmitted vouchers when work is started
    const isResubmittedVoucher = v.resubmissionCount && v.resubmissionCount > 0;
    if (isResubmittedVoucher) {
      return true; // Resubmitted vouchers with work started go to PENDING DISPATCH
    }

    // Regular vouchers (exclude permanent records)
    const isRegularVoucher = v.rejectionType !== 'PERMANENT_RECORD';

    // DUAL-TAB WORKFLOW: Include dispatchable rejected voucher copies
    const isDispatchableRejectedCopy = v.rejectionType === 'DISPATCHABLE_COPY' &&
                                      (v.isRejectionCopy === true || v.isRejectionCopy === 1);

    return isRegularVoucher || isDispatchableRejectedCopy;
  });
  // PERMANENT FIX: DISPATCHED tab logic depends on department context
  const dispatchedVouchers = vouchers.filter(v => {
    const isOriginalDepartment = v.originalDepartment === department;
    const isDispatchedByAudit = v.auditDispatchedBy && v.auditDispatchTime; // Must be dispatched by Audit
    const isCertified = v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED; // Must be certified
    const isNotDeleted = !v.deleted;

    // DEBUG: Log voucher details for FINJUL0003-COPY
    if (v.voucherId === 'FINJUL0003-COPY') {
      console.log('🔍 DISPATCHED TAB DEBUG - FINJUL0003-COPY:', {
        voucherId: v.voucherId,
        department: v.department,
        originalDepartment: v.originalDepartment,
        status: v.status,
        dispatched: v.dispatched,
        auditDispatchedBy: v.auditDispatchedBy,
        auditDispatchTime: v.auditDispatchTime,
        deleted: v.deleted,
        filteringFor: department
      });
    }

    if (department === 'AUDIT') {
      // AUDIT DISPATCHED TAB: Show vouchers dispatched FROM Audit TO other departments
      // These vouchers stay in AUDIT department until the target department receives them
      const isInAuditDepartment = v.department === 'AUDIT'; // Still in AUDIT awaiting receipt
      const isDispatched = v.dispatched === true; // Has been dispatched

      const result = isOriginalDepartment &&
                     isDispatchedByAudit &&
                     isInAuditDepartment &&
                     isDispatched &&
                     isCertified &&
                     isNotDeleted;

      // DEBUG: Log criteria for FINJUL0003-COPY
      if (v.voucherId === 'FINJUL0003-COPY') {
        console.log('🎯 AUDIT DISPATCHED CRITERIA - FINJUL0003-COPY:', {
          isOriginalDepartment,
          isDispatchedByAudit,
          isInAuditDepartment,
          isDispatched,
          isCertified,
          isNotDeleted,
          finalResult: result
        });
      }

      return result;
    } else {
      // DEPARTMENT DISPATCHED TAB: Show vouchers received back from Audit
      const isReceivedByDepartment = v.department === department; // Received back by this department

      return isOriginalDepartment &&
             isDispatchedByAudit &&
             isReceivedByDepartment &&
             isCertified &&
             isNotDeleted;
    }
  });

  const returnedVouchers = vouchers.filter(v =>
    v.originalDepartment === department &&
    v.status === VOUCHER_STATUSES.VOUCHER_RETURNED &&
    !v.deleted // Exclude deleted vouchers
  );

  const rejectedVouchers = vouchers.filter(v => {
    const isRejectedStatus = v.status === VOUCHER_STATUSES.VOUCHER_REJECTED;
    const isNotDeleted = !v.deleted;

    if (!isRejectedStatus || !isNotDeleted) {
      return false;
    }

    // DUAL-TAB WORKFLOW: Show rejected vouchers based on rejection type and department context

    if (department === 'AUDIT') {
      // For Audit department: Show PERMANENT RECORDS that are currently in AUDIT
      // (regardless of original department - they moved to AUDIT for audit trail)
      const isCurrentlyInAudit = v.department === 'AUDIT';
      const isPermanentRecord = v.rejectionType === 'PERMANENT_RECORD';
      return isCurrentlyInAudit && isPermanentRecord;
    } else {
      // PERMANENT FIX: For Finance department - show rejected voucher copies that were dispatched and received

      const isOriginalDepartment = v.originalDepartment === department;
      const isRejectedStatus = v.status === VOUCHER_STATUSES.VOUCHER_REJECTED;
      const isDispatchedByAudit = v.auditDispatchedBy && v.auditDispatchTime; // Must be dispatched by Audit
      const isReceivedByDepartment = v.department === department; // Must be received by this department
      const isRejectionCopy = v.isRejectionCopy === true || v.isRejectionCopy === 1; // Must be dispatchable copy

      // PERMANENT LOGIC: Only show VOUCHER_REJECTED dispatchable copies that were:
      // 1. Originally from this department
      // 2. Dispatched by Audit (rejection workflow completed)
      // 3. Received back by this department
      // 4. Have REJECTED status
      // 5. Are dispatchable copies (not permanent records)
      return isOriginalDepartment &&
             isRejectedStatus &&
             isDispatchedByAudit &&
             isReceivedByDepartment &&
             isRejectionCopy;
    }
  });

  // Tab counts for debugging (removed console logging to prevent loops)

  return {
    activeTab,
    setActiveTab,
    newVouchers,
    pendingDispatchVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers
  };
};
