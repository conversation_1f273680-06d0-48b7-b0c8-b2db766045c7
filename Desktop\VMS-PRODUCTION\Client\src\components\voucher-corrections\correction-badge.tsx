import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Edit3, History } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CorrectionBadgeProps {
  correctionCount?: number;
  lastCorrectedBy?: string;
  lastCorrectionTime?: string;
  className?: string;
  showIcon?: boolean;
  variant?: 'default' | 'compact';
}

export function CorrectionBadge({
  correctionCount = 0,
  lastCorrectedBy,
  lastCorrectionTime,
  className,
  showIcon = true,
  variant = 'default'
}: CorrectionBadgeProps) {
  if (correctionCount === 0) return null;

  const formatCorrectionTime = (timeString?: string) => {
    if (!timeString) return '';
    try {
      return new Date(timeString).toLocaleString();
    } catch {
      return timeString;
    }
  };

  const badgeText = variant === 'compact' 
    ? `${correctionCount}x`
    : `CORRECTED ${correctionCount > 1 ? `(${correctionCount}x)` : ''}`;

  const tooltipContent = (
    <div className="space-y-1">
      <div className="font-medium">Voucher Corrected</div>
      <div>Total corrections: {correctionCount}</div>
      {lastCorrectedBy && (
        <div>Last corrected by: {lastCorrectedBy}</div>
      )}
      {lastCorrectionTime && (
        <div>Last correction: {formatCorrectionTime(lastCorrectionTime)}</div>
      )}
    </div>
  );

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant="secondary"
            className={cn(
              "bg-orange-100 text-orange-800 border-orange-300 hover:bg-orange-200",
              "flex items-center gap-1",
              className
            )}
          >
            {showIcon && <Edit3 className="h-3 w-3" />}
            {badgeText}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          {tooltipContent}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

interface CorrectionHistoryBadgeProps {
  onClick: () => void;
  correctionCount: number;
  className?: string;
}

export function CorrectionHistoryBadge({
  onClick,
  correctionCount,
  className
}: CorrectionHistoryBadgeProps) {
  if (correctionCount === 0) return null;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant="outline"
            className={cn(
              "cursor-pointer hover:bg-blue-50 border-blue-300 text-blue-700",
              "flex items-center gap-1",
              className
            )}
            onClick={onClick}
          >
            <History className="h-3 w-3" />
            History ({correctionCount})
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>View correction history</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
