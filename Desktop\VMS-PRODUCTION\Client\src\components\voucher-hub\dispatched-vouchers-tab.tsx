
import React, { useState, useRef, useEffect } from 'react';
import { formatNumberWithCommas, formatVMSDateTime } from '@/utils/formatUtils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Voucher } from '@/lib/types';
import { SortableColumnHeader } from './sortable-column-header';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogHeader } from '@/components/ui/dialog';
import { ArrowUpDown, Download, Filter, Edit3, Eye } from 'lucide-react';
import { exportVouchersToExcel, groupVouchersByMonth } from '@/utils/exportUtils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { But<PERSON> } from '@/components/ui/button';
import { CorrectionModal } from '@/components/voucher-corrections/correction-modal';
import { CorrectionBadge } from '@/components/voucher-corrections/correction-badge';
import { useVoucherCorrections } from '@/hooks/use-voucher-corrections';
import { useAppStore } from '@/lib/store';

interface DispatchedVouchersTabProps {
  filteredVouchers: Voucher[];
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (column: string) => void;
  onViewVoucher?: (voucher: Voucher) => void;
  isEditable?: boolean;
  isAuditUser?: boolean;
}

export function DispatchedVouchersTab({
  filteredVouchers,
  sortColumn,
  sortDirection,
  handleSort,
  onViewVoucher,
  isEditable = true,
  isAuditUser = false,
}: DispatchedVouchersTabProps) {
  const [selectedVoucher, setSelectedVoucher] = useState<Voucher | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<string>('All');
  const [correctionModalOpen, setCorrectionModalOpen] = useState(false);
  const [voucherToCorrect, setVoucherToCorrect] = useState<Voucher | null>(null);

  const currentUser = useAppStore((state) => state.currentUser);
  const refreshVouchers = useAppStore((state) => state.refreshVouchers);

  // Check if current user is from AUDIT department
  const isUserAuditDept = currentUser?.department === 'AUDIT';

  // Use voucher corrections hook with real-time updates
  const { } = useVoucherCorrections((data) => {
    // Refresh vouchers when correction is received
    refreshVouchers();
  });

  // Refs for synchronized scrolling
  const headerRef = useRef<HTMLDivElement>(null);
  const bodyRef = useRef<HTMLDivElement>(null);

  // Handle opening correction modal
  const handleEditVoucher = (voucher: Voucher, e: React.MouseEvent) => {
    e.stopPropagation();
    setVoucherToCorrect(voucher);
    setCorrectionModalOpen(true);
  };

  // Handle correction applied
  const handleCorrectionApplied = (voucherId: string) => {
    setCorrectionModalOpen(false);
    setVoucherToCorrect(null);
    refreshVouchers();
  };

  // Handle closing voucher details dialog
  const handleCloseDialog = () => {
    setSelectedVoucher(null);
  };

  // Filter vouchers by selected month
  const vouchersByMonth = groupVouchersByMonth(filteredVouchers);

  // Get all months that have vouchers
  const availableMonths = Object.entries(vouchersByMonth)
    .filter(([_, vouchers]) => vouchers.length > 0)
    .map(([month]) => month);

  // Get the vouchers for the selected month or all vouchers if 'All' is selected
  const displayedVouchers = selectedMonth === 'All'
    ? filteredVouchers
    : vouchersByMonth[selectedMonth] || [];

  // Set up synchronized scrolling between header and body
  useEffect(() => {
    const headerElement = headerRef.current;
    const bodyElement = bodyRef.current;

    if (!headerElement || !bodyElement) return;

    const handleHeaderScroll = () => {
      if (bodyElement) {
        bodyElement.scrollLeft = headerElement.scrollLeft;
      }
    };

    const handleBodyScroll = () => {
      if (headerElement) {
        headerElement.scrollLeft = bodyElement.scrollLeft;
      }
    };

    headerElement.addEventListener('scroll', handleHeaderScroll);
    bodyElement.addEventListener('scroll', handleBodyScroll);

    return () => {
      headerElement.removeEventListener('scroll', handleHeaderScroll);
      bodyElement.removeEventListener('scroll', handleBodyScroll);
    };
  }, []);

  const handleViewVoucher = (voucher: Voucher) => {
    if (onViewVoucher) {
      onViewVoucher(voucher);
    } else {
      setSelectedVoucher(voucher);
    }
  };

  // Handle export to Excel
  const handleExport = () => {
    exportVouchersToExcel(
      displayedVouchers,
      `dispatched-vouchers-${selectedMonth.toLowerCase() !== 'all' ? selectedMonth.toLowerCase() + '-' : ''}${new Date().toISOString().split('T')[0]}`
    );
  };

  return (
    <div className="space-y-2">
      {/* Controls for filtering and export */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4" />
          <span className="text-sm font-medium">Filter by Month:</span>
          <Select value={selectedMonth} onValueChange={setSelectedMonth} disabled={!isEditable}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Month" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All Months</SelectItem>
              {availableMonths.map(month => (
                <SelectItem key={month} value={month}>{month}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button
          variant="outline"
          size="sm"
          className="flex items-center space-x-2"
          onClick={handleExport}
          disabled={!isEditable}
        >
          <Download className="h-4 w-4" />
          <span>Export to Excel</span>
        </Button>
      </div>

      <div className="flex flex-col rounded-md border">
        {/* Fixed header */}
        <div className="sticky top-0 z-10 bg-background overflow-hidden">
          <div ref={headerRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1600px' }}>
              <thead>
                <tr className="bg-background">
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('voucherId')}
                    >
                      <span>VOUCHER ID</span>
                      {sortColumn === 'voucherId' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[20%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('date')}
                    >
                      <span>DATE</span>
                      {sortColumn === 'date' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[20%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('claimant')}
                    >
                      <span>CLAIMANT</span>
                      {sortColumn === 'claimant' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[25%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('description')}
                    >
                      <span>DESCRIPTION</span>
                      {sortColumn === 'description' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[10%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('amount')}
                    >
                      <span>AMOUNT</span>
                      {sortColumn === 'amount' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('preAuditedAmount')}
                    >
                      <span>CERTIFIED AMT</span>
                      {sortColumn === 'preAuditedAmount' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('taxType')}
                    >
                      <span>TAX DETAILS</span>
                      {sortColumn === 'taxType' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[12%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('preAuditedBy')}
                    >
                      <span>PRE-AUDITED BY</span>
                      {sortColumn === 'preAuditedBy' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[12%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('certifiedBy')}
                    >
                      <span>CERTIFIED BY</span>
                      {sortColumn === 'certifiedBy' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('auditDispatchedBy')}
                    >
                      <span>DISPATCHED BY</span>
                      {sortColumn === 'auditDispatchedBy' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('auditDispatchTime')}
                    >
                      <span>DISPATCH TIME</span>
                      {sortColumn === 'auditDispatchTime' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[10%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">ACTIONS</th>
                </tr>
              </thead>
            </table>
          </div>
        </div>

        {/* Scrollable body */}
        <div className="overflow-auto h-[60vh] scrollbar-visible" style={{ scrollbarWidth: 'thin', overflowY: 'scroll' }}>
          <div ref={bodyRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1600px' }}>
              <tbody>
              {displayedVouchers.length === 0 ? (
                <tr className="border-b h-14">
                  <td colSpan={12} className="p-4 text-center uppercase font-medium">
                    {selectedMonth !== 'All'
                      ? `NO DISPATCHED VOUCHERS FOUND FOR ${selectedMonth.toUpperCase()}.`
                      : 'NO DISPATCHED VOUCHERS FOUND.'}
                  </td>
                </tr>
              ) : (
                displayedVouchers.map((voucher) => (
                  <tr
                    key={voucher.id}
                    className="cursor-pointer hover:bg-muted/50 border-b h-14"
                    onClick={() => handleViewVoucher(voucher)}
                  >
                    <td className="font-medium uppercase p-4 align-middle w-[15%] text-center">{voucher.voucherId}</td>
                    <td className="uppercase p-4 align-middle w-[20%] text-center">{voucher.date}</td>
                    <td className="uppercase p-4 align-middle w-[20%] text-center">{voucher.claimant}</td>
                    <td className="max-w-xs truncate uppercase p-4 align-middle w-[25%] text-center">{voucher.description}</td>
                    <td className="uppercase whitespace-nowrap p-4 align-middle w-[10%] text-center text-xs">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {formatNumberWithCommas(voucher.amount)} {voucher.currency}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{formatNumberWithCommas(voucher.amount)} {voucher.currency}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase whitespace-nowrap p-4 align-middle w-[15%] text-center text-xs">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase whitespace-nowrap p-4 align-middle w-[15%] text-center text-xs">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.taxType && voucher.taxAmount
                                ? `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}`
                                : (voucher.taxType || '-')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {voucher.taxType && voucher.taxAmount
                                ? `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}`
                                : (voucher.taxType || '-')}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[12%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.preAuditedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Pre-Audited By</p>
                              <p>{voucher.preAuditedBy || 'Not pre-audited'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[12%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.certifiedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Certified By</p>
                              <p>{voucher.certifiedBy || 'Not certified'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.auditDispatchedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Dispatched By</p>
                              <p>{voucher.auditDispatchedBy || 'Not dispatched'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.auditDispatchTime ? formatVMSDateTime(voucher.auditDispatchTime) : '-'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Dispatch Time</p>
                              <p>{voucher.auditDispatchTime ? formatVMSDateTime(voucher.auditDispatchTime) : 'Not dispatched'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="p-4 align-middle w-[10%] text-center">
                      <div className="flex items-center justify-center gap-2">
                        {onViewVoucher && (
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              onViewVoucher(voucher);
                            }}
                            title="View Voucher Details"
                            className="h-8 w-8"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}

                        {/* Show correction badge if voucher has been corrected */}
                        {voucher.is_corrected && (
                          <CorrectionBadge
                            correctionCount={voucher.correction_count || 0}
                            lastCorrectedBy={voucher.last_corrected_by}
                            lastCorrectionTime={voucher.last_correction_time}
                          />
                        )}

                        {/* Show edit button only for Audit users */}
                        {isUserAuditDept && (
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={(e) => handleEditVoucher(voucher, e)}
                            title="Edit Voucher"
                            className="h-8 w-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          >
                            <Edit3 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Voucher Details Dialog - shown only when onViewVoucher prop is not provided */}
      {selectedVoucher && !onViewVoucher && (
        <Dialog open={!!selectedVoucher} onOpenChange={() => handleCloseDialog()}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle className="uppercase">Voucher Details: {selectedVoucher.voucherId}</DialogTitle>
              <DialogDescription>
                Complete information about this voucher
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Voucher ID:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.voucherId}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Date:</div>
                <div className="col-span-3">{formatVMSDate(selectedVoucher.date)}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Claimant:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.claimant}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Description:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.description}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Amount:</div>
                <div className="col-span-3">
                  {formatNumberWithCommas(selectedVoucher.amount)} {selectedVoucher.currency}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Pre-Audited Amount:</div>
                <div className="col-span-3">
                  {selectedVoucher.preAuditedAmount
                    ? `${formatNumberWithCommas(selectedVoucher.preAuditedAmount)} ${selectedVoucher.currency}`
                    : '-'}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Tax Details:</div>
                <div className="col-span-3 uppercase">
                  {selectedVoucher.taxType && selectedVoucher.taxAmount
                    ? `${selectedVoucher.taxType}: ${formatNumberWithCommas(selectedVoucher.taxAmount)} ${selectedVoucher.currency}`
                    : (selectedVoucher.taxType || '-')}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Department:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.department}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Pre-Audited By:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.preAuditedBy || '-'}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Certified By:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.certifiedBy || '-'}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Dispatched By:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.auditDispatchedBy || '-'}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Dispatch Time:</div>
                <div className="col-span-3">
                  {selectedVoucher.auditDispatchTime ? formatVMSDateTime(selectedVoucher.auditDispatchTime) : '-'}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Department Receipt:</div>
                <div className="col-span-3">
                  {selectedVoucher.departmentReceiptTime
                    ? `Received by ${selectedVoucher.departmentReceivedBy} at ${selectedVoucher.departmentReceiptTime}`
                    : 'Not yet received by department'}
                </div>
              </div>
              {selectedVoucher.comment && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="font-semibold text-right">Comment:</div>
                  <div className="col-span-3 uppercase">{selectedVoucher.comment}</div>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Voucher Correction Modal */}
      <CorrectionModal
        isOpen={correctionModalOpen}
        onClose={() => {
          setCorrectionModalOpen(false);
          setVoucherToCorrect(null);
        }}
        voucher={voucherToCorrect}
        onCorrectionApplied={handleCorrectionApplied}
        isAuditUser={isUserAuditDept}
      />
    </div>
  );
}
