import { useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import { getSocket } from '@/lib/socket';

export interface VoucherCorrection {
  id: string;
  voucherId: string;
  correctedBy: string;
  correctionTime: string;
  fieldName: string;
  oldValue: string;
  newValue: string;
  reason: string;
  createdAt: string;
}

export interface CorrectionRequest {
  voucherId: string;
  corrections: {
    claimant?: string;
    description?: string;
    amount?: number;
    certifiedAmount?: number;
    tax?: number;
  };
  reason: string;
}

export interface CorrectionEligibility {
  canCorrect: boolean;
  reason?: string;
}

export function useVoucherCorrections(onCorrectionReceived?: (data: any) => void) {
  const [isLoading, setIsLoading] = useState(false);
  const [correctionHistory, setCorrectionHistory] = useState<VoucherCorrection[]>([]);

  // Listen for real-time correction events
  useEffect(() => {
    const socket = getSocket();
    if (!socket) return;

    const handleVoucherCorrected = (data: any) => {
      console.log('Received voucher correction event:', data);

      // Show toast notification
      toast.info(
        `Voucher ${data.voucherId} was corrected by ${data.correctedBy}`,
        {
          description: `${data.corrections?.length || 0} field(s) updated`,
          duration: 5000
        }
      );

      // Call callback if provided
      if (onCorrectionReceived) {
        onCorrectionReceived(data);
      }
    };

    socket.on('voucher_corrected', handleVoucherCorrected);

    return () => {
      socket.off('voucher_corrected', handleVoucherCorrected);
    };
  }, [onCorrectionReceived]);

  /**
   * Apply corrections to a voucher
   */
  const applyCorrections = useCallback(async (request: CorrectionRequest): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/voucher-corrections/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(request)
      });

      const result = await response.json();

      if (result.success) {
        toast.success(result.message);
        return true;
      } else {
        toast.error(result.message || 'Failed to apply corrections');
        return false;
      }

    } catch (error) {
      console.error('Error applying corrections:', error);
      toast.error('Failed to apply corrections. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Get correction history for a voucher
   */
  const getCorrectionHistory = useCallback(async (voucherId: string): Promise<VoucherCorrection[]> => {
    try {
      const response = await fetch(`/api/voucher-corrections/history/${voucherId}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        const history = data.corrections || [];
        setCorrectionHistory(history);
        return history;
      } else {
        console.error('Failed to fetch correction history');
        return [];
      }

    } catch (error) {
      console.error('Error fetching correction history:', error);
      return [];
    }
  }, []);

  /**
   * Check if a voucher can be corrected
   */
  const checkCorrectionEligibility = useCallback(async (voucherId: string): Promise<CorrectionEligibility> => {
    try {
      const response = await fetch(`/api/voucher-corrections/can-correct/${voucherId}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        return {
          canCorrect: data.canCorrect,
          reason: data.reason
        };
      } else {
        return {
          canCorrect: false,
          reason: 'Failed to check eligibility'
        };
      }

    } catch (error) {
      console.error('Error checking correction eligibility:', error);
      return {
        canCorrect: false,
        reason: 'Error checking eligibility'
      };
    }
  }, []);

  /**
   * Get all corrected vouchers
   */
  const getCorrectedVouchers = useCallback(async (department?: string) => {
    try {
      const url = department 
        ? `/api/voucher-corrections/corrected-vouchers?department=${department}`
        : '/api/voucher-corrections/corrected-vouchers';

      const response = await fetch(url, {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        return data.vouchers || [];
      } else {
        console.error('Failed to fetch corrected vouchers');
        return [];
      }

    } catch (error) {
      console.error('Error fetching corrected vouchers:', error);
      return [];
    }
  }, []);

  /**
   * Get correction statistics
   */
  const getCorrectionStats = useCallback(async (department?: string) => {
    try {
      const url = department 
        ? `/api/voucher-corrections/stats?department=${department}`
        : '/api/voucher-corrections/stats';

      const response = await fetch(url, {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        return data.stats || {};
      } else {
        console.error('Failed to fetch correction stats');
        return {};
      }

    } catch (error) {
      console.error('Error fetching correction stats:', error);
      return {};
    }
  }, []);

  return {
    isLoading,
    correctionHistory,
    applyCorrections,
    getCorrectionHistory,
    checkCorrectionEligibility,
    getCorrectedVouchers,
    getCorrectionStats
  };
}
