// DIRECT CHECK: What is the EXACT current state of the resubmitted voucher?
const mysql = require('mysql2/promise');

async function directCheckResubmittedVoucher() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('🔍 DIRECT CHECK: EXACT STATE OF RESUBMITTED VOUCHER');
    console.log('===================================================');
    
    // Get ALL current resubmitted vouchers to find the one you're referring to
    const [vouchers] = await connection.execute(`
      SELECT voucher_id, status, department, original_department,
             resubmission_count, work_started, dispatched, received_by_audit,
             is_rejection_copy, rejection_type, created_at
      FROM vouchers
      WHERE resubmission_count > 0
        AND deleted = FALSE
      ORDER BY created_at DESC
    `);
    
    if (vouchers.length === 0) {
      console.log('❌ FINJUL0008-COPY not found');
      return;
    }
    
    const voucher = vouchers[0];
    console.log(`\n📋 EXACT CURRENT STATE: ${voucher.voucher_id}`);
    console.log('=========================================');
    console.log(`   Status: "${voucher.status}"`);
    console.log(`   Department: "${voucher.department}"`);
    console.log(`   Original Department: "${voucher.original_department}"`);
    console.log(`   Resubmission Count: ${voucher.resubmission_count}`);
    console.log(`   Work Started: ${voucher.work_started}`);
    console.log(`   Dispatched: ${voucher.dispatched}`);
    console.log(`   Received By Audit: ${voucher.received_by_audit}`);
    console.log(`   Is Rejection Copy: ${voucher.is_rejection_copy}`);
    console.log(`   Rejection Type: "${voucher.rejection_type || 'NULL'}"`);
    
    console.log(`\n🔍 FRONTEND TAB LOGIC SIMULATION:`);
    console.log('=================================');
    
    // Simulate EXACT frontend logic for NEW VOUCHER tab
    console.log(`\nNEW VOUCHER TAB LOGIC:`);
    console.log('---------------------');
    const newVoucherCheck1 = voucher.original_department === 'FINANCE';
    const newVoucherCheck2 = voucher.department === 'AUDIT';
    const newVoucherCheck3 = voucher.status === 'AUDIT PROCESSING';
    const newVoucherCheck4 = voucher.received_by_audit === 1;
    const newVoucherCheck5 = voucher.work_started !== 1; // workStarted !== true
    const newVoucherCheck6 = voucher.dispatched === 0; // !dispatched
    const newVoucherCheck7 = voucher.resubmission_count > 0; // isResubmittedVoucher
    const newVoucherCheck8 = voucher.is_rejection_copy === 0; // !isRejectionCopy
    
    console.log(`   originalDepartment === 'FINANCE': ${newVoucherCheck1 ? '✅' : '❌'}`);
    console.log(`   department === 'AUDIT': ${newVoucherCheck2 ? '✅' : '❌'}`);
    console.log(`   status === 'AUDIT PROCESSING': ${newVoucherCheck3 ? '✅' : '❌'}`);
    console.log(`   receivedByAudit === true: ${newVoucherCheck4 ? '✅' : '❌'}`);
    console.log(`   workStarted !== true: ${newVoucherCheck5 ? '✅' : '❌'}`);
    console.log(`   !dispatched: ${newVoucherCheck6 ? '✅' : '❌'}`);
    console.log(`   isResubmittedVoucher: ${newVoucherCheck7 ? '✅' : '❌'}`);
    console.log(`   !isRejectionCopy: ${newVoucherCheck8 ? '✅' : '❌'}`);
    
    const shouldBeInNewVoucher = newVoucherCheck1 && newVoucherCheck2 && newVoucherCheck3 && 
                                newVoucherCheck4 && newVoucherCheck5 && newVoucherCheck6 && 
                                newVoucherCheck7 && newVoucherCheck8;
    
    console.log(`\n🎯 SHOULD BE IN NEW VOUCHER: ${shouldBeInNewVoucher ? '✅ YES' : '❌ NO'}`);
    
    // Simulate EXACT frontend logic for PENDING DISPATCH tab
    console.log(`\nPENDING DISPATCH TAB LOGIC:`);
    console.log('--------------------------');
    const pendingCheck1 = voucher.original_department === 'FINANCE';
    const pendingCheck2 = voucher.department === 'AUDIT';
    const pendingCheck3 = voucher.dispatched === 0; // !dispatched
    
    // PATH 2: Resubmitted vouchers with work started
    const pendingPath2Check1 = voucher.status === 'AUDIT PROCESSING';
    const pendingPath2Check2 = voucher.received_by_audit === 1;
    const pendingPath2Check3 = voucher.work_started === 1; // workStarted === true
    const pendingPath2Check4 = voucher.resubmission_count > 0;
    const pendingPath2Check5 = voucher.is_rejection_copy === 0;
    
    console.log(`   originalDepartment === 'FINANCE': ${pendingCheck1 ? '✅' : '❌'}`);
    console.log(`   department === 'AUDIT': ${pendingCheck2 ? '✅' : '❌'}`);
    console.log(`   !dispatched: ${pendingCheck3 ? '✅' : '❌'}`);
    console.log(`   PATH 2 - Resubmitted with work started:`);
    console.log(`     status === 'AUDIT PROCESSING': ${pendingPath2Check1 ? '✅' : '❌'}`);
    console.log(`     receivedByAudit === true: ${pendingPath2Check2 ? '✅' : '❌'}`);
    console.log(`     workStarted === true: ${pendingPath2Check3 ? '✅' : '❌'}`);
    console.log(`     resubmissionCount > 0: ${pendingPath2Check4 ? '✅' : '❌'}`);
    console.log(`     !isRejectionCopy: ${pendingPath2Check5 ? '✅' : '❌'}`);
    
    const shouldBeInPendingDispatch = pendingCheck1 && pendingCheck2 && pendingCheck3 &&
                                     pendingPath2Check1 && pendingPath2Check2 && pendingPath2Check3 &&
                                     pendingPath2Check4 && pendingPath2Check5;
    
    console.log(`\n🎯 SHOULD BE IN PENDING DISPATCH: ${shouldBeInPendingDispatch ? '✅ YES' : '❌ NO'}`);
    
    console.log(`\n🔍 PROBLEM ANALYSIS:`);
    console.log('====================');
    
    if (shouldBeInPendingDispatch && !shouldBeInNewVoucher) {
      console.log('❌ CRITICAL ISSUE: work_started = 1 is causing the problem');
      console.log(`   Current work_started: ${voucher.work_started}`);
      console.log(`   This makes it appear in PENDING DISPATCH instead of NEW VOUCHER`);
      console.log('');
      console.log('🔍 WHY IS work_started = 1?');
      console.log('   1. Was it set during batch receipt?');
      console.log('   2. Did someone open the voucher and save it?');
      console.log('   3. Is there a bug in the backend logic?');
      console.log('   4. Is the frontend updating it incorrectly?');
      
    } else if (shouldBeInNewVoucher && !shouldBeInPendingDispatch) {
      console.log('✅ Voucher should correctly appear in NEW VOUCHER tab');
      console.log('   If it is appearing in PENDING DISPATCH, there may be a frontend caching issue');
      
    } else if (shouldBeInNewVoucher && shouldBeInPendingDispatch) {
      console.log('⚠️  Voucher matches both tabs - this should not happen');
      
    } else {
      console.log('❓ Voucher does not match either tab criteria - unexpected state');
    }
    
    console.log(`\n🎯 IMMEDIATE ACTION REQUIRED:`);
    console.log('=============================');
    console.log('Tell me EXACTLY what you see in the frontend:');
    console.log('1. Which tab is the voucher appearing in?');
    console.log('2. What is the current work_started value you see?');
    console.log('3. Has anyone opened/edited this voucher?');
    console.log('');
    console.log('Based on the database, this voucher should be in:');
    if (shouldBeInNewVoucher) {
      console.log('✅ NEW VOUCHER tab (work_started = 0)');
    }
    if (shouldBeInPendingDispatch) {
      console.log('❌ PENDING DISPATCH tab (work_started = 1)');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

directCheckResubmittedVoucher();
