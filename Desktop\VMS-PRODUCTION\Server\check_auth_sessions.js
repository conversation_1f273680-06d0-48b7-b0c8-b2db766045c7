const mysql = require('mysql2/promise');

async function checkAuthSessions() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  console.log('🔐 CHECKING AUTHENTICATION & SESSIONS');
  console.log('=====================================');

  try {
    // 1. Check active sessions
    const [activeSessions] = await connection.execute(`
      SELECT 
        user_id,
        user_name,
        department,
        session_start,
        last_activity,
        is_active,
        socket_id
      FROM active_sessions 
      WHERE is_active = TRUE
      ORDER BY last_activity DESC
    `);

    console.log(`📊 ACTIVE SESSIONS: ${activeSessions.length}`);
    console.log('============================');

    if (activeSessions.length > 0) {
      activeSessions.forEach((session, index) => {
        console.log(`${index + 1}. ${session.user_name} (${session.department})`);
        console.log(`   User ID: ${session.user_id}`);
        console.log(`   Session Start: ${session.session_start}`);
        console.log(`   Last Activity: ${session.last_activity}`);
        console.log(`   Socket ID: ${session.socket_id || 'None'}`);
        console.log('');
      });
    } else {
      console.log('❌ NO ACTIVE SESSIONS FOUND');
      console.log('This explains the 401 Unauthorized error!');
    }

    // 2. Check users table
    const [users] = await connection.execute(`
      SELECT
        id,
        name,
        department
      FROM users
      ORDER BY name
    `);

    console.log(`👥 TOTAL USERS: ${users.length}`);
    console.log('===================');

    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.department}) - ID: ${user.id}`);
    });

    // 3. Check for ASIEDU user specifically
    const [asieduUser] = await connection.execute(`
      SELECT
        id,
        name,
        department
      FROM users
      WHERE name LIKE '%ASIEDU%'
    `);

    console.log(`\n🔍 ASIEDU USER CHECK: ${asieduUser.length} found`);
    console.log('=============================');

    if (asieduUser.length > 0) {
      asieduUser.forEach((user, index) => {
        console.log(`${index + 1}. Name: ${user.name}`);
        console.log(`   ID: ${user.id}`);
        console.log(`   Department: ${user.department}`);

        console.log('');
      });
    } else {
      console.log('❌ No ASIEDU user found in database');
    }

    // 4. Check session store (if using express-session with database)
    try {
      const [sessionStore] = await connection.execute(`
        SHOW TABLES LIKE 'sessions'
      `);

      if (sessionStore.length > 0) {
        const [sessions] = await connection.execute(`
          SELECT 
            session_id,
            expires,
            data
          FROM sessions 
          WHERE expires > NOW()
          ORDER BY expires DESC
        `);

        console.log(`💾 SESSION STORE: ${sessions.length} active sessions`);
        console.log('=======================================');

        if (sessions.length > 0) {
          sessions.forEach((session, index) => {
            console.log(`${index + 1}. Session ID: ${session.session_id.substring(0, 20)}...`);
            console.log(`   Expires: ${session.expires}`);
            try {
              const sessionData = JSON.parse(session.data);
              if (sessionData.user) {
                console.log(`   User: ${sessionData.user.name} (${sessionData.user.department})`);
              }
            } catch (e) {
              console.log(`   Data: [Unable to parse]`);
            }
            console.log('');
          });
        }
      } else {
        console.log('💾 No session store table found (using memory store)');
      }
    } catch (sessionError) {
      console.log('💾 Session store check skipped (table may not exist)');
    }

    console.log('\n🔧 AUTHENTICATION ISSUE DIAGNOSIS:');
    console.log('===================================');

    if (activeSessions.length === 0) {
      console.log('❌ ISSUE: No active sessions found');
      console.log('🔧 SOLUTION: User needs to log in again');
      console.log('📋 Steps:');
      console.log('1. Go to login page');
      console.log('2. Enter credentials');
      console.log('3. Navigate to Audit dashboard');
      console.log('4. Check NEW VOUCHER tab');
    } else {
      console.log('✅ Active sessions found');
      console.log('❓ Issue might be:');
      console.log('- Session cookie not being sent');
      console.log('- CORS/authentication middleware issue');
      console.log('- Frontend not including credentials in requests');
    }

    console.log('\n🎯 NEXT STEPS:');
    console.log('==============');
    console.log('1. Log out completely');
    console.log('2. Clear browser cookies/cache');
    console.log('3. Log back in');
    console.log('4. Check if voucher appears in NEW VOUCHER tab');

  } catch (error) {
    console.error('❌ Error checking authentication sessions:', error.message);
  } finally {
    await connection.end();
  }
}

// Run the check
checkAuthSessions().catch(console.error);
