"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const voucher_corrections_service_js_1 = require("../services/voucher-corrections.service.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
const socketHandlers_js_1 = require("../socket/socketHandlers.js");
const db_js_1 = require("../database/db.js");
const router = express_1.default.Router();
/**
 * Apply corrections to a completed voucher
 * POST /api/voucher-corrections/apply
 */
router.post('/apply', auth_js_1.authenticate, async (req, res) => {
    try {
        const { voucherId, corrections, reason } = req.body;
        const correctedBy = req.user?.name || 'Unknown User';
        // Validation
        if (!voucherId || !corrections || !reason) {
            return res.status(400).json({
                success: false,
                message: 'Missing required fields: voucherId, corrections, and reason are required'
            });
        }
        // Check if user is from AUDIT department (only audit users can make corrections)
        if (req.user?.department !== 'AUDIT') {
            return res.status(403).json({
                success: false,
                message: 'Only Audit department users can make voucher corrections'
            });
        }
        // Validate corrections object
        const allowedFields = ['claimant', 'description', 'amount', 'certifiedAmount', 'tax'];
        const providedFields = Object.keys(corrections);
        const invalidFields = providedFields.filter(field => !allowedFields.includes(field));
        if (invalidFields.length > 0) {
            return res.status(400).json({
                success: false,
                message: `Invalid correction fields: ${invalidFields.join(', ')}. Allowed fields: ${allowedFields.join(', ')}`
            });
        }
        if (providedFields.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No correction fields provided'
            });
        }
        // Validate numeric fields
        const numericFields = ['amount', 'certifiedAmount', 'tax'];
        for (const field of numericFields) {
            if (corrections[field] !== undefined && (isNaN(corrections[field]) || corrections[field] < 0)) {
                return res.status(400).json({
                    success: false,
                    message: `Invalid value for ${field}. Must be a positive number.`
                });
            }
        }
        // Apply corrections
        const correctionRequest = {
            voucherId,
            corrections,
            reason: reason.trim(),
            correctedBy
        };
        const result = await voucher_corrections_service_js_1.VoucherCorrectionsService.applyCorrections(correctionRequest);
        if (result.success) {
            logger_js_1.logger.info(`Voucher correction applied successfully by ${correctedBy} for voucher ${voucherId}`);
            // Get updated voucher data for real-time broadcast
            try {
                const [vouchers] = await (0, db_js_1.query)(`SELECT * FROM vouchers WHERE id = ? OR voucher_id = ?`, [voucherId, voucherId]);
                if (Array.isArray(vouchers) && vouchers.length > 0) {
                    const updatedVoucher = vouchers[0];
                    // Broadcast real-time correction update
                    (0, socketHandlers_js_1.broadcastVoucherCorrection)(voucherId, correctedBy, result.corrections || [], updatedVoucher);
                }
            }
            catch (broadcastError) {
                logger_js_1.logger.error('Error broadcasting voucher correction:', broadcastError);
                // Don't fail the request if broadcast fails
            }
            res.status(200).json(result);
        }
        else {
            res.status(400).json(result);
        }
    }
    catch (error) {
        logger_js_1.logger.error('Error in voucher correction endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while applying corrections'
        });
    }
});
/**
 * Get correction history for a voucher
 * GET /api/voucher-corrections/history/:voucherId
 */
router.get('/history/:voucherId', auth_js_1.authenticate, async (req, res) => {
    try {
        const { voucherId } = req.params;
        if (!voucherId) {
            return res.status(400).json({
                success: false,
                message: 'Voucher ID is required'
            });
        }
        const history = await voucher_corrections_service_js_1.VoucherCorrectionsService.getCorrectionHistory(voucherId);
        res.status(200).json({
            success: true,
            voucherId,
            corrections: history,
            totalCorrections: history.length
        });
    }
    catch (error) {
        logger_js_1.logger.error('Error fetching correction history:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching correction history'
        });
    }
});
/**
 * Check if a voucher can be corrected
 * GET /api/voucher-corrections/can-correct/:voucherId
 */
router.get('/can-correct/:voucherId', auth_js_1.authenticate, async (req, res) => {
    try {
        const { voucherId } = req.params;
        if (!voucherId) {
            return res.status(400).json({
                success: false,
                message: 'Voucher ID is required'
            });
        }
        const eligibility = await voucher_corrections_service_js_1.VoucherCorrectionsService.canCorrectVoucher(voucherId);
        res.status(200).json({
            success: true,
            voucherId,
            ...eligibility
        });
    }
    catch (error) {
        logger_js_1.logger.error('Error checking voucher correction eligibility:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while checking voucher eligibility'
        });
    }
});
/**
 * Get all corrected vouchers
 * GET /api/voucher-corrections/corrected-vouchers
 */
router.get('/corrected-vouchers', auth_js_1.authenticate, async (req, res) => {
    try {
        const { department } = req.query;
        const correctedVouchers = await voucher_corrections_service_js_1.VoucherCorrectionsService.getCorrectedVouchers(department);
        res.status(200).json({
            success: true,
            vouchers: correctedVouchers,
            totalCount: correctedVouchers.length
        });
    }
    catch (error) {
        logger_js_1.logger.error('Error fetching corrected vouchers:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching corrected vouchers'
        });
    }
});
/**
 * Get correction statistics
 * GET /api/voucher-corrections/stats
 */
router.get('/stats', auth_js_1.authenticate, async (req, res) => {
    try {
        const { department } = req.query;
        // Get basic correction statistics
        const correctedVouchers = await voucher_corrections_service_js_1.VoucherCorrectionsService.getCorrectedVouchers(department);
        const stats = {
            totalCorrectedVouchers: correctedVouchers.length,
            totalCorrections: correctedVouchers.reduce((sum, voucher) => sum + (voucher.total_corrections || 0), 0),
            averageCorrectionsPerVoucher: correctedVouchers.length > 0
                ? (correctedVouchers.reduce((sum, voucher) => sum + (voucher.total_corrections || 0), 0) / correctedVouchers.length).toFixed(2)
                : 0,
            recentCorrections: correctedVouchers.slice(0, 5) // Last 5 corrected vouchers
        };
        res.status(200).json({
            success: true,
            stats
        });
    }
    catch (error) {
        logger_js_1.logger.error('Error fetching correction statistics:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching correction statistics'
        });
    }
});
exports.default = router;
//# sourceMappingURL=voucher-corrections.js.map