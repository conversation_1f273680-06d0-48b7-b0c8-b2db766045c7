export interface VoucherCorrection {
    id: string;
    voucherId: string;
    correctedBy: string;
    correctionTime: string;
    fieldName: string;
    oldValue: string | null;
    newValue: string | null;
    reason: string;
    createdAt: string;
}
export interface CorrectionRequest {
    voucherId: string;
    corrections: {
        claimant?: string;
        description?: string;
        amount?: number;
        certifiedAmount?: number;
        tax?: number;
    };
    reason: string;
    correctedBy: string;
}
export interface CorrectionResponse {
    success: boolean;
    message: string;
    correctionId?: string;
    corrections?: VoucherCorrection[];
}
/**
 * Voucher Corrections Service
 * Handles post-completion voucher corrections with full audit trail
 */
export declare class VoucherCorrectionsService {
    /**
     * Apply corrections to a completed voucher
     */
    static applyCorrections(request: CorrectionRequest): Promise<CorrectionResponse>;
    /**
     * Get correction history for a voucher
     */
    static getCorrectionHistory(voucherId: string): Promise<VoucherCorrection[]>;
    /**
     * Check if a voucher can be corrected
     */
    static canCorrectVoucher(voucherId: string): Promise<{
        canCorrect: boolean;
        reason?: string;
    }>;
    /**
     * Map frontend field names to database column names
     */
    private static mapFieldNameToDatabase;
    /**
     * Get vouchers that have been corrected
     */
    static getCorrectedVouchers(department?: string): Promise<any[]>;
}
