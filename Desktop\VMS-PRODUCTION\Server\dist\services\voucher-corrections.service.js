"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoucherCorrectionsService = void 0;
const uuid_1 = require("uuid");
const db_js_1 = require("../database/db.js");
const logger_js_1 = require("../utils/logger.js");
/**
 * Voucher Corrections Service
 * Handles post-completion voucher corrections with full audit trail
 */
class VoucherCorrectionsService {
    /**
     * Apply corrections to a completed voucher
     */
    static async applyCorrections(request) {
        try {
            logger_js_1.logger.info(`Applying corrections to voucher ${request.voucherId} by ${request.correctedBy}`);
            return await (0, db_js_1.withTransaction)(async (connection) => {
                // Step 1: Validate voucher exists and is eligible for correction
                const [vouchers] = await connection.execute(`SELECT id, voucher_id, claimant, description, amount, pre_audited_amount, tax_amount,
           status, department, is_corrected, correction_count
           FROM vouchers WHERE id = ? OR voucher_id = ?`, [request.voucherId, request.voucherId]);
                if (!Array.isArray(vouchers) || vouchers.length === 0) {
                    return {
                        success: false,
                        message: 'Voucher not found'
                    };
                }
                const voucher = vouchers[0];
                // Step 2: Validate voucher is eligible for correction (completed status)
                const eligibleStatuses = ['CERTIFIED', 'DISPATCHED', 'AUDIT: PROCESSING'];
                if (!eligibleStatuses.includes(voucher.status)) {
                    return {
                        success: false,
                        message: `Voucher cannot be corrected. Current status: ${voucher.status}. Only CERTIFIED or DISPATCHED vouchers can be corrected.`
                    };
                }
                // Step 3: Apply corrections and log each change
                const correctionId = (0, uuid_1.v4)();
                const correctionTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
                const appliedCorrections = [];
                // Build update query and log corrections
                const updateFields = [];
                const updateValues = [];
                // Process each correction field
                for (const [fieldName, newValue] of Object.entries(request.corrections)) {
                    if (newValue !== undefined && newValue !== null) {
                        const dbFieldName = this.mapFieldNameToDatabase(fieldName);
                        const oldValue = voucher[dbFieldName];
                        // Only apply if value actually changed
                        if (oldValue !== newValue) {
                            updateFields.push(`${dbFieldName} = ?`);
                            updateValues.push(newValue);
                            // Log the correction
                            const correctionLogId = (0, uuid_1.v4)();
                            await connection.execute(`INSERT INTO voucher_corrections 
                 (id, voucher_id, corrected_by, correction_time, field_name, old_value, new_value, reason)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [correctionLogId, voucher.id, request.correctedBy, correctionTime, fieldName,
                                String(oldValue || ''), String(newValue), request.reason]);
                            appliedCorrections.push({
                                id: correctionLogId,
                                voucherId: voucher.id,
                                correctedBy: request.correctedBy,
                                correctionTime,
                                fieldName,
                                oldValue: String(oldValue || ''),
                                newValue: String(newValue),
                                reason: request.reason,
                                createdAt: correctionTime
                            });
                            logger_js_1.logger.info(`Correction applied: ${fieldName} changed from "${oldValue}" to "${newValue}" for voucher ${voucher.voucher_id}`);
                        }
                    }
                }
                if (updateFields.length === 0) {
                    return {
                        success: false,
                        message: 'No changes detected. All provided values match current voucher data.'
                    };
                }
                // Step 4: Update voucher with corrections
                const newCorrectionCount = (voucher.correction_count || 0) + 1;
                updateFields.push('correction_count = ?', 'last_corrected_by = ?', 'last_correction_time = ?', 'correction_reason = ?', 'is_corrected = ?');
                updateValues.push(newCorrectionCount, request.correctedBy, correctionTime, request.reason, true);
                const updateQuery = `UPDATE vouchers SET ${updateFields.join(', ')} WHERE id = ?`;
                updateValues.push(voucher.id);
                await connection.execute(updateQuery, updateValues);
                logger_js_1.logger.info(`Voucher ${voucher.voucher_id} successfully corrected. Total corrections: ${newCorrectionCount}`);
                return {
                    success: true,
                    message: `Voucher corrected successfully. ${appliedCorrections.length} field(s) updated.`,
                    correctionId,
                    corrections: appliedCorrections
                };
            });
        }
        catch (error) {
            logger_js_1.logger.error('Error applying voucher corrections:', error);
            return {
                success: false,
                message: 'Failed to apply corrections. Please try again.'
            };
        }
    }
    /**
     * Get correction history for a voucher
     */
    static async getCorrectionHistory(voucherId) {
        try {
            const [corrections] = await (0, db_js_1.query)(`SELECT id, voucher_id, corrected_by, correction_time, field_name, 
         old_value, new_value, reason, created_at
         FROM voucher_corrections 
         WHERE voucher_id = (SELECT id FROM vouchers WHERE id = ? OR voucher_id = ?)
         ORDER BY correction_time DESC`, [voucherId, voucherId]);
            return corrections.map(correction => ({
                id: correction.id,
                voucherId: correction.voucher_id,
                correctedBy: correction.corrected_by,
                correctionTime: correction.correction_time,
                fieldName: correction.field_name,
                oldValue: correction.old_value,
                newValue: correction.new_value,
                reason: correction.reason,
                createdAt: correction.created_at
            }));
        }
        catch (error) {
            logger_js_1.logger.error('Error fetching correction history:', error);
            return [];
        }
    }
    /**
     * Check if a voucher can be corrected
     */
    static async canCorrectVoucher(voucherId) {
        try {
            const [vouchers] = await (0, db_js_1.query)(`SELECT status, department FROM vouchers WHERE id = ? OR voucher_id = ?`, [voucherId, voucherId]);
            if (!Array.isArray(vouchers) || vouchers.length === 0) {
                return { canCorrect: false, reason: 'Voucher not found' };
            }
            const voucher = vouchers[0];
            const eligibleStatuses = ['CERTIFIED', 'DISPATCHED', 'AUDIT: PROCESSING'];
            if (!eligibleStatuses.includes(voucher.status)) {
                return {
                    canCorrect: false,
                    reason: `Voucher status "${voucher.status}" is not eligible for correction. Only CERTIFIED or DISPATCHED vouchers can be corrected.`
                };
            }
            return { canCorrect: true };
        }
        catch (error) {
            logger_js_1.logger.error('Error checking voucher correction eligibility:', error);
            return { canCorrect: false, reason: 'Error checking voucher eligibility' };
        }
    }
    /**
     * Map frontend field names to database column names
     */
    static mapFieldNameToDatabase(fieldName) {
        const fieldMap = {
            'claimant': 'claimant',
            'description': 'description',
            'amount': 'amount',
            'certifiedAmount': 'pre_audited_amount',
            'tax': 'tax_amount'
        };
        return fieldMap[fieldName] || fieldName;
    }
    /**
     * Get vouchers that have been corrected
     */
    static async getCorrectedVouchers(department) {
        try {
            let query_str = `
        SELECT v.*, 
               (SELECT COUNT(*) FROM voucher_corrections vc WHERE vc.voucher_id = v.id) as total_corrections
        FROM vouchers v 
        WHERE v.is_corrected = TRUE
      `;
            const params = [];
            if (department) {
                query_str += ` AND (v.department = ? OR v.original_department = ?)`;
                params.push(department, department);
            }
            query_str += ` ORDER BY v.last_correction_time DESC`;
            const [vouchers] = await (0, db_js_1.query)(query_str, params);
            return vouchers;
        }
        catch (error) {
            logger_js_1.logger.error('Error fetching corrected vouchers:', error);
            return [];
        }
    }
}
exports.VoucherCorrectionsService = VoucherCorrectionsService;
//# sourceMappingURL=voucher-corrections.service.js.map