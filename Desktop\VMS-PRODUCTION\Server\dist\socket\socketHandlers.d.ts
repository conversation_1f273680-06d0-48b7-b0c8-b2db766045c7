import { Server } from 'socket.io';
export declare function setupSocketHandlers(io: Server): void;
export declare function setIoInstance(io: Server): void;
export declare function broadcastUserUpdate(type: string, userData: any): void;
export declare function broadcastRegistrationUpdate(type: string, registrationData: any): void;
export declare function broadcastVoucherUpdate(type: string, voucherData: any): void;
/**
 * Broadcast voucher correction events to relevant departments
 */
export declare function broadcastVoucherCorrection(voucherId: string, correctedBy: string, corrections: any[], voucherData: any): void;
export declare function broadcastBatchUpdate(type: string, batchData: any): void;
export declare function broadcastNotificationUpdate(type: string, notificationData: any): void;
export declare function broadcastDataUpdate(entityType: string, actionType: string, data: any): void;
